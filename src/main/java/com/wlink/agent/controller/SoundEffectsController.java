package com.wlink.agent.controller;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.model.dto.QueueStatus;
import com.wlink.agent.model.dto.SoundEffectsRequest;
import com.wlink.agent.model.dto.SoundEffectsResult;
import com.wlink.agent.model.dto.StableAudioResult;
import com.wlink.agent.model.req.GenerateSoundEffectReq;
import com.wlink.agent.model.req.GenerateStableAudioReq;
import com.wlink.agent.model.res.GenerateSoundEffectRes;
import com.wlink.agent.model.res.GenerateStableAudioRes;
import com.wlink.agent.service.SoundEffectsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

import java.util.concurrent.CompletableFuture;

/**
 * 音效生成控制器
 * 使用 JDK 17 新特性
 */
@Tag(name = "音效生成", description = "AI音效生成相关接口")
@Slf4j
@RestController
@RequestMapping("/agent/sound-effects")
@RequiredArgsConstructor
public class SoundEffectsController {

    private final SoundEffectsService soundEffectsService;

    /**
     * 提交音效生成请求
     *
     * @param request 音效生成请求
     * @return 队列状态
     */
    @PostMapping("/submit")
    @Operation(summary = "提交音效生成请求")
    public CompletableFuture<SingleResponse<QueueStatus>> submitRequest(
            @Valid @RequestBody SoundEffectsRequest request) {

        log.info("提交音效生成请求: {}", request.getDescription());

        return soundEffectsService.submitRequest(request)
                .thenApply(SingleResponse::of)
                .exceptionally(throwable -> {
                    log.error("提交音效生成请求失败", throwable);
                    return SingleResponse.buildFailure("SUBMIT_FAILED", throwable.getMessage());
                });
    }

    /**
     * 查询请求状态
     *
     * @param requestId 请求ID
     * @return 队列状态
     */
    @GetMapping("/status/{requestId}")
    @Operation(summary = "查询音效生成状态")
    public CompletableFuture<SingleResponse<QueueStatus>> getStatus(
            @Parameter(description = "请求ID", required = true)
            @PathVariable("requestId") String requestId) {

        log.info("查询音效生成状态: requestId={}", requestId);

        return soundEffectsService.getStatus(requestId)
                .thenApply(SingleResponse::of)
                .exceptionally(throwable -> {
                    log.error("查询音效生成状态失败: requestId={}", requestId, throwable);
                    return SingleResponse.buildFailure("QUERY_FAILED", throwable.getMessage());
                });
    }

    /**
     * 获取生成结果
     *
     * @param requestId 请求ID
     * @return 音效生成结果
     */
    @GetMapping("/result/{requestId}")
    @Operation(summary = "获取音效生成结果")
    public CompletableFuture<SingleResponse<SoundEffectsResult>> getResult(
            @Parameter(description = "请求ID", required = true)
            @PathVariable("requestId") String requestId) {

        log.info("获取音效生成结果: requestId={}", requestId);

        return soundEffectsService.getResult(requestId)
                .thenApply(SingleResponse::of)
                .exceptionally(throwable -> {
                    log.error("获取音效生成结果失败: requestId={}", requestId, throwable);
                    return SingleResponse.buildFailure("GET_RESULT_FAILED", throwable.getMessage());
                });
    }

    /**
     * 取消请求
     *
     * @param requestId 请求ID
     * @return 是否取消成功
     */
    @PutMapping("/cancel/{requestId}")
    @Operation(summary = "取消音效生成请求")
    public CompletableFuture<SingleResponse<Boolean>> cancelRequest(
            @Parameter(description = "请求ID", required = true)
            @PathVariable("requestId") String requestId) {

        log.info("取消音效生成请求: requestId={}", requestId);

        return soundEffectsService.cancelRequest(requestId)
                .thenApply(SingleResponse::of)
                .exceptionally(throwable -> {
                    log.error("取消音效生成请求失败: requestId={}", requestId, throwable);
                    return SingleResponse.buildFailure("CANCEL_FAILED", throwable.getMessage());
                });
    }

    /**
     * 一键生成音效（同步等待结果）
     *
     * @param prompt   音效描述
     * @param duration 时长（秒）
     * @return 音效生成结果
     */
    @PostMapping("/generate")
    @Operation(summary = "一键生成音效", description = "提交请求并等待生成完成")
    public SingleResponse<SoundEffectsResult> generateSoundEffect(
            @Parameter(description = "音效描述", required = true)
            @RequestParam("prompt") @NotBlank String prompt,

            @Parameter(description = "时长（秒）", required = false)
            @RequestParam(value = "duration", defaultValue = "30")
            @Min(1) @Max(30) Integer duration) {

        log.info("一键生成音效: prompt='{}', duration={}秒", prompt, duration);

        try {
            SoundEffectsResult result = soundEffectsService.generateSoundEffect(prompt, duration).join();
            log.info("一键生成音效成功: prompt='{}', duration={}秒, audioUrl={}",
                    prompt, duration, result.getAudioUrl());
            return SingleResponse.of(result);
        } catch (Exception e) {
            log.error("一键生成音效失败: prompt='{}', duration={}秒", prompt, duration, e);
            return SingleResponse.buildFailure("GENERATE_FAILED", e.getMessage());
        }
    }

    /**
     * 快速生成音效（默认30秒）
     *
     * @param prompt 音效描述
     * @return 音效生成结果
     */
    @PostMapping("/quick-generate")
    @Operation(summary = "快速生成音效", description = "使用默认30秒时长快速生成音效")
    public SingleResponse<SoundEffectsResult> quickGenerate(
            @Parameter(description = "音效描述", required = true)
            @RequestParam("prompt") @NotBlank String prompt) {

        log.info("快速生成音效: prompt='{}'", prompt);

        try {
            SoundEffectsResult result = soundEffectsService.generateSoundEffect(prompt).join();
            log.info("快速生成音效成功: prompt='{}', audioUrl={}", prompt, result.getAudioUrl());
            return SingleResponse.of(result);
        } catch (Exception e) {
            log.error("快速生成音效失败: prompt='{}'", prompt, e);
            return SingleResponse.buildFailure("QUICK_GENERATE_FAILED", e.getMessage());
        }
    }

    /**
     * 生成音效并返回音频URL
     *
     * @param req 生成音效请求
     * @return 音效生成结果（包含音频URL）
     */
    @PostMapping("/generate-audio-url")
    @Operation(summary = "生成音效并返回音频URL", description = "调用AI生成音效，等待完成后返回音频下载地址")
    public SingleResponse<GenerateSoundEffectRes> generateAudio(
            @Valid @RequestBody GenerateSoundEffectReq req) {

        log.info("生成音效请求: prompt='{}', duration={}秒, conversationId={}, contentId={}, index={}", 
                req.getPrompt(), req.getDuration(), req.getConversationId(), req.getContentId(), req.getIndex());

        try {
            SoundEffectsResult result = soundEffectsService.generateSoundEffect(
                    req.getPrompt(), 
                    req.getDuration(), 
                    req.getConversationId(),
                    req.getContentId(),
                    req.getIndex()
            ).join();

            if (!result.isValid()) {
                log.error("音效生成结果无效: prompt='{}'", req.getPrompt());
                throw new BizException("INVALID_RESULT", "音效生成失败，返回结果无效");
            }

            // 构建响应对象
            GenerateSoundEffectRes response = GenerateSoundEffectRes.of(
                    result.getAudioUrl(),
                    req.getPrompt(),
                    req.getDuration()
            );

            // 设置文件信息
            if (result.audioFile() != null) {
                response.withFileInfo(
                        result.audioFile().fileName(),
                        result.audioFile().getFormattedFileSize(),
                        result.audioFile().contentType()
                );
            }

            log.info("音效生成成功: prompt='{}', audioUrl={}", req.getPrompt(), result.getAudioUrl());
            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("生成音效失败: prompt='{}', duration={}秒", req.getPrompt(), req.getDuration(), e);
            throw new BizException("GENERATE_AUDIO_FAILED",
                    "音效生成失败: " + e.getMessage());
        }
    }

    /**
     * 快速生成音效（简化版接口）
     *
     * @param prompt 音效描述
     * @return 音频URL
     */
    @PostMapping("/generate-simple")
    @Operation(summary = "快速生成音效", description = "简化版接口，只需要提示词，返回音频URL")
    public SingleResponse<String> generateSimple(
            @Parameter(description = "音效描述", required = true)
            @RequestParam("prompt") @NotBlank String prompt) {

        log.info("快速生成音效: prompt='{}'", prompt);

        try {
            SoundEffectsResult result = soundEffectsService.generateSoundEffect(prompt, 30).join();

            if (!result.isValid()) {
                log.error("音效生成结果无效: prompt='{}'", prompt);
                throw new BizException("INVALID_RESULT", "音效生成失败，返回结果无效");
            }

            String audioUrl = result.getAudioUrl();
            log.info("快速音效生成成功: prompt='{}', audioUrl={}", prompt, audioUrl);
            return SingleResponse.of(audioUrl);

        } catch (Exception e) {
            log.error("快速生成音效失败: prompt='{}'", prompt, e);
            throw new BizException("GENERATE_SIMPLE_FAILED",
                    "音效生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成 Stable Audio 并返回音频URL
     *
     * @param req 生成 Stable Audio 请求
     * @return Stable Audio 生成结果（包含音频URL）
     */
    @PostMapping("/generate-audio")
    @Operation(summary = "生成 Stable Audio 并返回音频URL", description = "调用 Stable Audio API 生成高质量音频，等待完成后返回音频下载地址")
    public SingleResponse<GenerateStableAudioRes> generateStableAudio(
            @Valid @RequestBody GenerateStableAudioReq req) {

        log.info("生成 Stable Audio 请求: prompt='{}', duration={}秒, steps={}, secondsStart={}, conversationId={}, contentId={}, index={}",
                req.getPrompt(), req.getDuration(), req.getSteps(), req.getSecondsStart(),
                req.getConversationId(), req.getContentId(), req.getIndex());

        // 验证请求参数
        if (!req.isValid()) {
            log.error("Stable Audio 请求参数无效: {}", req.getDescription());
            throw new BizException("INVALID_REQUEST", "请求参数无效");
        }

        long startTime = System.currentTimeMillis();

        try {
            // 调用 SoundEffectsServiceImpl 的新方法
            StableAudioResult result = soundEffectsService
                    .generateStableAudio(
                            req.getPrompt(),
                            req.getDuration(),
                            req.getConversationId(),
                            req.getContentId(),
                            req.getIndex()
                    );

            long generationTime = System.currentTimeMillis() - startTime;

            if (!result.isValid()) {
                log.error("Stable Audio 生成结果无效: prompt='{}'", req.getPrompt());
                throw new BizException("INVALID_RESULT", "Stable Audio 生成失败，返回结果无效");
            }

            // 构建响应对象
            GenerateStableAudioRes response = GenerateStableAudioRes.of(
                    result.getAudioUrl(),
                    req.getPrompt(),
                    req.getDuration(),
                    req.getSteps(),
                    req.getSecondsStart()
            );

            // 设置文件信息
            if (result.audioFile() != null) {
                response.withFileInfo(
                        result.audioFile().fileName(),
                        result.audioFile().getFormattedFileSize(),
                        result.audioFile().contentType()
                );
            }

            // 设置生成信息
            response.withGenerationInfo("SUCCESS", generationTime);

            log.info("Stable Audio 生成成功: prompt='{}', duration={}秒, audioUrl={}, generationTime={}ms",
                    req.getPrompt(), req.getDuration(), result.getAudioUrl(), generationTime);

            return SingleResponse.of(response);

        } catch (Exception e) {
            long generationTime = System.currentTimeMillis() - startTime;
            log.error("生成 Stable Audio 失败: prompt='{}', duration={}秒, generationTime={}ms",
                    req.getPrompt(), req.getDuration(), generationTime, e);

            throw new BizException("GENERATE_STABLE_AUDIO_FAILED",
                    "Stable Audio 生成失败: " + e.getMessage());
        }
    }

}
