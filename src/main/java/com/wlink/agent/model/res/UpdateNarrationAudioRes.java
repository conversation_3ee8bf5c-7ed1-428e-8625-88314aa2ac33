package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 修改音频响应
 */
@Data
@Schema(description = "修改音频响应")
public class UpdateNarrationAudioRes {

    @Schema(description = "会话ID")
    @JsonProperty("sessionId")
    private String sessionId;

    @Schema(description = "角色ID，如果为空则表示修改旁白")
    @JsonProperty("characterId")
    private String characterId;

    @Schema(description = "音频类型：narration-旁白，character-角色")
    @JsonProperty("audioType")
    private String audioType;

    @Schema(description = "处理的章节数量")
    @JsonProperty("processedChapterCount")
    private Integer processedChapterCount;

    @Schema(description = "成功生成的音频数量")
    @JsonProperty("successCount")
    private Integer successCount;

    @Schema(description = "失败的音频数量")
    @JsonProperty("failureCount")
    private Integer failureCount;

    @Schema(description = "处理详情列表")
    @JsonProperty("processDetails")
    private List<ProcessDetail> processDetails;

    /**
     * 处理详情
     */
    @Data
    @Schema(description = "处理详情")
    public static class ProcessDetail {

        @Schema(description = "章节ID")
        @JsonProperty("segmentId")
        private String segmentId;

        @Schema(description = "章节名称")
        @JsonProperty("segmentName")
        private String segmentName;

        @Schema(description = "分镜ID")
        @JsonProperty("shotId")
        private String shotId;

        @Schema(description = "旁白ID")
        @JsonProperty("narrationId")
        private Integer narrationId;

        @Schema(description = "旁白内容")
        @JsonProperty("narrationLine")
        private String narrationLine;

        @Schema(description = "处理状态：SUCCESS-成功，FAILED-失败")
        @JsonProperty("status")
        private String status;

        @Schema(description = "生成的音频URL")
        @JsonProperty("audioUrl")
        private String audioUrl;

        @Schema(description = "音频时长（毫秒）")
        @JsonProperty("duration")
        private Long duration;

        @Schema(description = "TTS记录ID")
        @JsonProperty("recordId")
        private Long recordId;

        @Schema(description = "错误信息（失败时）")
        @JsonProperty("errorMessage")
        private String errorMessage;
    }
}
