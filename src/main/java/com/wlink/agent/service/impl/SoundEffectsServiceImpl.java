package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.client.FalSoundEffectsClient;
import com.wlink.agent.client.FalStableAudioClient;
import com.wlink.agent.dao.mapper.AiCanvasMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiSoundEffectsRecordMapper;
import com.wlink.agent.dao.po.AiCanvasPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiSoundEffectsRecordPo;
import com.wlink.agent.model.dto.QueueStatus;
import com.wlink.agent.model.dto.SoundEffectsRequest;
import com.wlink.agent.model.dto.SoundEffectsResult;
import com.wlink.agent.model.dto.StableAudioRequest;
import com.wlink.agent.model.dto.StableAudioResult;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.service.SoundEffectsService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 音效生成服务实现
 * 使用 JDK 17 新特性
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SoundEffectsServiceImpl implements SoundEffectsService {

    private final FalSoundEffectsClient falSoundEffectsClient;
    private final FalStableAudioClient falStableAudioClient;
    private final OssUtils ossUtils;
    private final AiCreationSessionMapper aiCreationSessionMapper;
    private final AiSoundEffectsRecordMapper aiSoundEffectsRecordMapper;
    private final AiCanvasMapper canvasMapper;

    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 默认最大等待时间（分钟）
     */
    private static final int DEFAULT_MAX_WAIT_MINUTES = 10;

    /**
     * OSS 音频文件路径模板
     */
    private static final String OSS_AUDIO_PATH = "dify/{env}/{userId}/{type}/";

    /**
     * 支持的音频文件扩展名
     */
    private static final java.util.Set<String> AUDIO_EXTENSIONS = java.util.Set.of("wav", "mp3", "ogg", "m4a", "flac");
    
    @Override
    public CompletableFuture<QueueStatus> submitRequest(SoundEffectsRequest request) {
        log.info("提交音效生成请求: {}", request.getDescription());
        
        // 验证请求参数
        if (!request.isValid()) {
            return CompletableFuture.failedFuture(
                    new IllegalArgumentException("无效的音效生成请求参数"));
        }
        
        return falSoundEffectsClient.submitRequest(request)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("提交音效生成请求失败: {}", request.getDescription(), throwable);
                    } else {
                        log.info("音效生成请求提交成功: {}, requestId={}", 
                                request.getDescription(), result.requestId());
                    }
                });
    }
    
    @Override
    public CompletableFuture<QueueStatus> getStatus(String requestId) {
        log.debug("查询音效生成状态: requestId={}", requestId);
        
        return falSoundEffectsClient.getStatus(requestId, false)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("查询音效生成状态失败: requestId={}", requestId, throwable);
                    } else {
                        log.debug("音效生成状态: {}", result.getFormattedStatus());
                    }
                });
    }
    
    @Override
    public CompletableFuture<SoundEffectsResult> getResult(String requestId) {
        log.debug("获取音效生成结果: requestId={}", requestId);
        
        return falSoundEffectsClient.getResult(requestId)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("获取音效生成结果失败: requestId={}", requestId, throwable);
                    } else {
                        log.info("获取音效生成结果成功: requestId={}, {}", 
                                requestId, result.getDescription());
                    }
                });
    }
    
    @Override
    public CompletableFuture<Boolean> cancelRequest(String requestId) {
        log.info("取消音效生成请求: requestId={}", requestId);
        
        return falSoundEffectsClient.cancelRequest(requestId)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("取消音效生成请求失败: requestId={}", requestId, throwable);
                    } else {
                        log.info("取消音效生成请求结果: requestId={}, success={}", requestId, result);
                    }
                });
    }

    @Override
    public CompletableFuture<SoundEffectsResult> generateSoundEffect(String prompt, int duration) {
       return generateSoundEffect(prompt, duration, "");
    }

    @Override
    public CompletableFuture<SoundEffectsResult> generateSoundEffect(String prompt, int duration,String conversationId) {
        log.info("开始生成音效: prompt='{}', duration={}秒,conversationId={}", prompt, duration,conversationId);

        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, conversationId));
        if (aiCreationSessionPo == null) {
            log.error("会话不存在");
            throw new RuntimeException("会话不存在");
        }

        // 创建音频生成记录
        AiSoundEffectsRecordPo generationLog = createSoundEffectsRecord(
                aiCreationSessionPo.getUserId(), conversationId, prompt, duration, null, null, 1);
        
        long startTime = System.currentTimeMillis();

        return falSoundEffectsClient.generateAndWait(prompt, duration, DEFAULT_MAX_WAIT_MINUTES)
                .thenApply(result -> {
                    long generationTime = System.currentTimeMillis() - startTime;
                    
                    if (!result.isValid()) {
                        log.error("音效生成结果无效: prompt='{}'", prompt);
                        // 更新记录为失败状态
                        updateSoundEffectsRecordFailed(generationLog.getId(), "GENERATION_FAILED", 
                                "音效生成结果无效", generationTime);
                        return result;
                    }

                    try {
                        // 上传音频文件到 OSS
                        String ossAudioUrl = uploadAudioToOss(result.getAudioUrl(),aiCreationSessionPo.getUserId());

                        // 创建新的结果对象，使用 OSS URL 替换原始 URL
                        var newAudioFile = new SoundEffectsResult.AudioFile(
                                MediaUrlPrefixUtil.getMediaUrl(ossAudioUrl),
                                result.audioFile().fileSize(),
                                result.audioFile().fileName(),
                                result.audioFile().contentType()
                        );

                        var newResult = new SoundEffectsResult(newAudioFile);

                        // 更新记录为成功状态
                        updateSoundEffectsRecordSuccess(generationLog.getId(), result.getAudioUrl(), 
                                ossAudioUrl, result.audioFile().fileName(), result.audioFile().fileSize(),
                                result.audioFile().contentType(), generationTime);

                        log.info("音效生成并上传成功: prompt='{}', duration={}秒, originalUrl={}, ossUrl={}",
                                prompt, duration, result.getAudioUrl(), ossAudioUrl);

                        return newResult;

                    } catch (Exception e) {
                        log.error("音效上传到OSS失败: prompt='{}', audioUrl={}", prompt, result.getAudioUrl(), e);
                        // 更新记录为上传失败状态
                        updateSoundEffectsRecordFailed(generationLog.getId(), "UPLOAD_FAILED", 
                                "上传到OSS失败: " + e.getMessage(), generationTime);
                        // 上传失败时返回原始结果
                        return result;
                    }
                })
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        long generationTime = System.currentTimeMillis() - startTime;
                        log.error("音效生成失败: prompt='{}', duration={}秒", prompt, duration, throwable);
                        // 更新记录为失败状态
                        updateSoundEffectsRecordFailed(generationLog.getId(), "GENERATION_ERROR", 
                                throwable.getMessage(), generationTime);
                    }
                });
    }
    
    @Override
    public CompletableFuture<SoundEffectsResult> generateSoundEffect(String prompt, int duration, String conversationId, String contentId, Integer index) {
        log.info("开始生成音效: prompt='{}', duration={}秒, conversationId={}, contentId={}, index={}", 
                prompt, duration, conversationId, contentId, index);

        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, conversationId));
        if (aiCreationSessionPo == null) {
            log.error("会话不存在");
            throw new BizException("会话不存在");
        }

        // 创建音频生成记录
        AiSoundEffectsRecordPo generationLog = createSoundEffectsRecord(
                aiCreationSessionPo.getUserId(), conversationId, prompt, duration, contentId, index, 1);
        
        long startTime = System.currentTimeMillis();

        return falSoundEffectsClient.generateAndWait(prompt, duration, DEFAULT_MAX_WAIT_MINUTES)
                .thenApply(result -> {
                    long generationTime = System.currentTimeMillis() - startTime;
                    
                    if (!result.isValid()) {
                        log.error("音效生成结果无效: prompt='{}'", prompt);
                        // 更新记录为失败状态
                        updateSoundEffectsRecordFailed(generationLog.getId(), "GENERATION_FAILED", 
                                "音效生成结果无效", generationTime);
                        return result;
                    }

                    try {
                        // 上传音频文件到 OSS
                        String ossAudioUrl = uploadAudioToOss(result.getAudioUrl(),aiCreationSessionPo.getUserId());

                        // 创建新的结果对象，使用 OSS URL 替换原始 URL
                        var newAudioFile = new SoundEffectsResult.AudioFile(
                               ossAudioUrl,
                                result.audioFile().fileSize(),
                                result.audioFile().fileName(),
                                result.audioFile().contentType()
                        );

                        var newResult = new SoundEffectsResult(newAudioFile);

                        // 更新记录为成功状态
                        updateSoundEffectsRecordSuccess(generationLog.getId(), result.getAudioUrl(), 
                                ossAudioUrl, result.audioFile().fileName(), result.audioFile().fileSize(),
                                result.audioFile().contentType(), generationTime);

                        log.info("音效生成并上传成功: prompt='{}', duration={}秒, originalUrl={}, ossUrl={}",
                                prompt, duration, result.getAudioUrl(), ossAudioUrl);

                        return newResult;

                    } catch (Exception e) {
                        log.error("音效上传到OSS失败: prompt='{}', audioUrl={}", prompt, result.getAudioUrl(), e);
                        // 更新记录为上传失败状态
                        updateSoundEffectsRecordFailed(generationLog.getId(), "UPLOAD_FAILED", 
                                "上传到OSS失败: " + e.getMessage(), generationTime);
                        // 上传失败时返回原始结果
                        return result;
                    }
                })
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        long generationTime = System.currentTimeMillis() - startTime;
                        log.error("音效生成失败: prompt='{}', duration={}秒", prompt, duration, throwable);
                        // 更新记录为失败状态
                        updateSoundEffectsRecordFailed(generationLog.getId(), "GENERATION_ERROR", 
                                throwable.getMessage(), generationTime);
                    }
                });
    }

    @Override
    public CompletableFuture<SoundEffectsResult> generateSoundEffect(String prompt) {
        return generateSoundEffect(prompt, 30,"");
    }

    @Override
    public SoundEffectsResult generateSoundEffectForCanvas(String prompt, int duration, String conversationId, String contentId, Integer index) {
        log.info("开始为画布生成音效: prompt='{}', duration={}秒, conversationId={}, contentId={}, index={}",
                prompt, duration, conversationId, contentId, index);

        // 查询画布信息获取用户ID
        AiCanvasPo canvasPo = canvasMapper.selectOne(new LambdaQueryWrapper<AiCanvasPo>()
                .eq(AiCanvasPo::getCode, conversationId));
        if (canvasPo == null) {
            log.error("画布不存在: code={}", conversationId);
            throw new BizException("画布不存在");
        }

        // 创建音频生成记录，source设置为2（画布）
        AiSoundEffectsRecordPo generationLog = createSoundEffectsRecord(
                canvasPo.getUserId(), conversationId, prompt, duration, contentId, index, 2);

        long startTime = System.currentTimeMillis();

        try {
            // 同步生成音效
            SoundEffectsResult result = falSoundEffectsClient.generateAndWait(prompt, duration, DEFAULT_MAX_WAIT_MINUTES)
                    .get(); // 使用get()方法同步等待结果

            long generationTime = System.currentTimeMillis() - startTime;

            if (!result.isValid()) {
                log.error("音效生成结果无效: prompt='{}'", prompt);
                // 更新记录为失败状态
                updateSoundEffectsRecordFailed(generationLog.getId(), "GENERATION_FAILED",
                        "音效生成结果无效", generationTime);
                throw new BizException("音效生成失败");
            }

            try {
                // 上传音频文件到 OSS
                String ossAudioUrl = uploadAudioToOss(result.getAudioUrl(), canvasPo.getUserId());

                // 创建新的结果对象，使用 OSS URL 替换原始 URL
                var newAudioFile = new SoundEffectsResult.AudioFile(
                        ossAudioUrl,
                        result.audioFile().fileSize(),
                        result.audioFile().fileName(),
                        result.audioFile().contentType()
                );

                var newResult = new SoundEffectsResult(newAudioFile);

                // 更新记录为成功状态
                updateSoundEffectsRecordSuccess(generationLog.getId(), result.getAudioUrl(),
                        ossAudioUrl, result.audioFile().fileName(), result.audioFile().fileSize(),
                        result.audioFile().contentType(), generationTime);

                log.info("画布音效生成并上传成功: prompt='{}', duration={}秒, originalUrl={}, ossUrl={}",
                        prompt, duration, result.getAudioUrl(), ossAudioUrl);

                return newResult;

            } catch (Exception e) {
                log.error("音效上传到OSS失败: prompt='{}', audioUrl={}", prompt, result.getAudioUrl(), e);
                // 更新记录为上传失败状态
                updateSoundEffectsRecordFailed(generationLog.getId(), "UPLOAD_FAILED",
                        "上传到OSS失败: " + e.getMessage(), generationTime);
                throw new BizException("音效上传失败: " + e.getMessage());
            }

        } catch (Exception e) {
            long generationTime = System.currentTimeMillis() - startTime;
            log.error("画布音效生成失败: prompt='{}', duration={}秒", prompt, duration, e);
            // 更新记录为失败状态
            updateSoundEffectsRecordFailed(generationLog.getId(), "GENERATION_ERROR",
                    e.getMessage(), generationTime);
            throw new BizException("音效生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量生成音效
     * 使用 JDK 17 的 CompletableFuture.allOf 和 Stream API
     *
     * @param requests 音效生成请求列表
     * @return 音效生成结果列表
     */
    public CompletableFuture<List<SoundEffectsResult>> generateBatch(
           List<SoundEffectsRequest> requests) {
        
        log.info("开始批量生成音效: 共{}个请求", requests.size());
        
        // 使用 JDK 17 的 Stream API 和 CompletableFuture
        var futures = requests.stream()
                .map(request -> generateSoundEffect(request.prompt(), request.duration(),""))
                .toArray(CompletableFuture[]::new);
        
        return CompletableFuture.allOf(futures)
                .thenApply(v -> java.util.Arrays.stream(futures)
                        .map(CompletableFuture::join)
                        .map(obj -> (SoundEffectsResult) obj)
                        .toList()) // JDK 17 的 toList() 方法
                .whenComplete((results, throwable) -> {
                    if (throwable != null) {
                        log.error("批量音效生成失败", throwable);
                    } else {
                        long successCount = results.stream()
                                .filter(SoundEffectsResult::isValid)
                                .count();
                        log.info("批量音效生成完成: 成功{}/{}个", successCount, requests.size());
                    }
                });
    }
    
    /**
     * 生成音效并保存到指定位置
     * 使用 JDK 17 的 switch 表达式和 pattern matching
     *
     * @param prompt 音效描述
     * @param duration 时长
     * @param savePath 保存路径
     * @return 保存的文件路径
     */
    public CompletableFuture<String> generateAndSave(String prompt, int duration, String savePath) {
        return generateSoundEffect(prompt, duration,"")
                .thenCompose(result -> {
                    if (!result.isValid()) {
                        return CompletableFuture.failedFuture(
                                new RuntimeException("音效生成结果无效"));
                    }
                    
                    // 这里可以添加下载和保存文件的逻辑
                    // 使用 JDK 17 的 switch 表达式处理不同的文件类型
                    var fileExtension = result.audioFile().getFileExtension();
                    var finalPath = switch (fileExtension != null ? fileExtension : "default") {
                        case "wav", "mp3", "ogg" -> savePath + "." + fileExtension;
                        case "default" -> savePath + ".wav"; // 默认扩展名
                        default -> savePath + ".audio";
                    };
                    
                    log.info("音效将保存到: {}", finalPath);
                    
                    // TODO: 实现实际的文件下载和保存逻辑
                    return CompletableFuture.completedFuture(finalPath);
                });
    }

    /**
     * 上传音频文件到 OSS
     *
     * @param audioUrl 原始音频URL
     * @return OSS音频URL
     */
    private String uploadAudioToOss(String audioUrl,String userId) {
        try {
            // 获取当前用户ID

            // 确定文件扩展名
            String fileExtension = getAudioFileExtension(audioUrl);

            // 构建OSS路径
            String ossPath = OSS_AUDIO_PATH.replace("{env}", env)
                    .replace("{userId}", userId)
                    .replace("{type}", "audio");

            // 生成唯一文件名
            String fileName = IdUtil.fastSimpleUUID() + "." + fileExtension;
            String fullOssPath = ossPath + fileName;

            log.info("开始上传音频到OSS: originalUrl={}, ossPath={}", audioUrl, fullOssPath);

            // 调用 OSS 工具类上传文件
            String ossUrl = ossUtils.uploadFile(audioUrl, fullOssPath);

            log.info("音频上传到OSS成功: originalUrl={}, ossUrl={}", audioUrl, ossUrl);
            return ossUrl;

        } catch (Exception e) {
            log.error("上传音频到OSS失败: audioUrl={}", audioUrl, e);
            throw new RuntimeException("上传音频到OSS失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从URL中提取音频文件扩展名
     *
     * @param audioUrl 音频URL
     * @return 文件扩展名
     */
    private String getAudioFileExtension(String audioUrl) {
        if (audioUrl == null || audioUrl.isEmpty()) {
            return "wav"; // 默认扩展名
        }

        // 从URL中提取文件名
        String fileName = audioUrl.substring(audioUrl.lastIndexOf('/') + 1);

        // 移除查询参数
        if (fileName.contains("?")) {
            fileName = fileName.substring(0, fileName.indexOf("?"));
        }

        // 提取扩展名
        if (fileName.contains(".")) {
            String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

            // 验证是否为支持的音频格式
            if (AUDIO_EXTENSIONS.contains(extension)) {
                return extension;
            }
        }

        // 默认返回 wav 格式
        return "wav";
    }

    /**
     * 创建音效记录
     *
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param prompt 音效描述
     * @param duration 时长
     * @param contentId 内容ID
     * @param index 音频索引
     * @param source 来源(1-故事,2-画布)
     * @return 音效记录
     */
    private AiSoundEffectsRecordPo createSoundEffectsRecord(String userId, String sessionId,
                                                           String prompt, int duration, String contentId, Integer index, Integer source) {
        AiSoundEffectsRecordPo generationLog = new AiSoundEffectsRecordPo();
        generationLog.setUserId(userId);
        generationLog.setSessionId(sessionId);
        generationLog.setContentId(contentId);
        generationLog.setAudioIndex(index);
        generationLog.setPrompt(prompt);
        generationLog.setDuration(duration);
        generationLog.setSource(source);
        generationLog.setGenerationStatus("PROCESSING");
        
        aiSoundEffectsRecordMapper.insert(generationLog);
        
        log.info("创建音效记录: id={}, userId={}, contentId={}, index={}, prompt='{}'", 
                generationLog.getId(), userId, contentId, index, prompt);
        
        return generationLog;
    }

    /**
     * 更新音效记录为成功状态
     *
     * @param logId 记录ID
     * @param originalAudioUrl 原始音频URL
     * @param ossAudioUrl OSS音频URL
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param contentType 文件类型
     * @param generationTime 生成耗时
     */
    private void updateSoundEffectsRecordSuccess(Long logId, String originalAudioUrl, 
                                               String ossAudioUrl, String fileName, 
                                               Long fileSize, String contentType, 
                                               Long generationTime) {
        AiSoundEffectsRecordPo updateLog = new AiSoundEffectsRecordPo();
        updateLog.setId(logId);
        updateLog.setOriginalAudioUrl(originalAudioUrl);
        updateLog.setOssAudioUrl(ossAudioUrl);
        updateLog.setFileName(fileName);
        updateLog.setFileSize(fileSize);
        updateLog.setContentType(contentType);
        updateLog.setGenerationStatus("SUCCESS");
        updateLog.setGenerationTime(generationTime);
        
        aiSoundEffectsRecordMapper.updateById(updateLog);
        
        log.info("更新音效记录为成功: id={}, ossUrl={}", logId, ossAudioUrl);
    }

    /**
     * 更新音效记录为成功状态（简化版本）
     *
     * @param logId 记录ID
     * @param ossAudioUrl OSS音频URL
     * @param generationTime 生成耗时
     */
    private void updateSoundEffectsRecordSuccess(Long logId, String ossAudioUrl, Long generationTime) {
        AiSoundEffectsRecordPo updateLog = new AiSoundEffectsRecordPo();
        updateLog.setId(logId);
        updateLog.setOssAudioUrl(ossAudioUrl);
        updateLog.setGenerationStatus("SUCCESS");
        updateLog.setGenerationTime(generationTime);

        aiSoundEffectsRecordMapper.updateById(updateLog);

        log.info("更新音效记录为成功: id={}, ossUrl={}", logId, ossAudioUrl);
    }

    /**
     * 更新音效记录为失败状态
     *
     * @param logId 记录ID
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @param generationTime 生成耗时
     */
    private void updateSoundEffectsRecordFailed(Long logId, String errorCode, 
                                              String errorMessage, Long generationTime) {
        AiSoundEffectsRecordPo updateLog = new AiSoundEffectsRecordPo();
        updateLog.setId(logId);
        updateLog.setGenerationStatus("FAILED");
        updateLog.setErrorCode(errorCode);
        updateLog.setErrorMessage(errorMessage);
        updateLog.setGenerationTime(generationTime);
        
        aiSoundEffectsRecordMapper.updateById(updateLog);
        
        log.warn("更新音效记录为失败: id={}, errorCode={}, errorMessage={}",
                logId, errorCode, errorMessage);
    }

    /**
     * 使用 Stable Audio 生成音效（新接口）
     *
     * @param prompt 音频生成提示词
     * @param duration 音频时长（秒）
     * @param conversationId 会话ID
     * @param contentId 内容ID
     * @param index 索引
     * @return Stable Audio 生成结果
     */
    @Override
    public StableAudioResult generateStableAudio(String prompt, int duration, String conversationId, String contentId, Integer index) {
        log.info("开始使用 Stable Audio 生成音效: prompt='{}', duration={}秒, conversationId={}, contentId={}, index={}",
                prompt, duration, conversationId, contentId, index);



        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, conversationId));
        if (aiCreationSessionPo == null) {
            log.error("会话不存在");
            throw new BizException("会话不存在");
        }

        // 创建音频生成记录，source设置为3（Stable Audio）
        AiSoundEffectsRecordPo generationLog = createSoundEffectsRecord(
                aiCreationSessionPo.getUserId(), conversationId, prompt, duration, contentId, index, 3);

        long startTime = System.currentTimeMillis();

        try {
            // 使用 Stable Audio 同步生成音效
            StableAudioResult result = falStableAudioClient.generateAndWait(prompt, duration, DEFAULT_MAX_WAIT_MINUTES)
                    .get(); // 使用get()方法同步等待结果

            long generationTime = System.currentTimeMillis() - startTime;

            if (!result.isValid()) {
                log.error("Stable Audio 生成结果无效: prompt='{}'", prompt);
                // 更新记录为失败状态
                updateSoundEffectsRecordFailed(generationLog.getId(), "GENERATION_FAILED",
                        "Stable Audio 生成结果无效", generationTime);
                throw new BizException("Stable Audio 生成失败");
            }

            // 上传音频文件到OSS
            String audioUrl = result.getAudioUrl();
            String ossPath = OSS_AUDIO_PATH.replace("{env}", env)
                    .replace("{userId}", aiCreationSessionPo.getUserId())
                    .replace("{type}", "stable-audio");

            String uploadedUrl = ossUtils.uploadFile(audioUrl, ossPath + IdUtil.fastSimpleUUID() + ".wav");
            log.info("Stable Audio 文件上传成功: originalUrl={}, uploadedUrl={}", audioUrl, uploadedUrl);

            // 更新记录为成功状态
            updateSoundEffectsRecordSuccess(generationLog.getId(), audioUrl, uploadedUrl,
                    result.audioFile().fileName(), result.audioFile().fileSize(),
                    result.audioFile().contentType(), generationTime);

            // 返回带有新URL的结果
            StableAudioResult finalResult = result.withNewUrl(uploadedUrl);
            log.info("Stable Audio 生成完成: prompt='{}', duration={}秒, audioUrl={}",
                    prompt, duration, uploadedUrl);

            return finalResult;

        } catch (Exception e) {
            long generationTime = System.currentTimeMillis() - startTime;
            log.error("Stable Audio 生成失败: prompt='{}', duration={}秒", prompt, duration, e);

            // 更新记录为失败状态
            updateSoundEffectsRecordFailed(generationLog.getId(), "GENERATION_ERROR",
                    e.getMessage(), generationTime);

            throw new BizException("Stable Audio 生成失败: " + e.getMessage());
        }
    }

    @Override
    public CompletableFuture<StableAudioResult> generateStableAudioAsync(String prompt, int duration) {
        log.info("开始异步生成 Stable Audio: prompt='{}', duration={}秒", prompt, duration);

        return falStableAudioClient.generateAndWait(prompt, duration, DEFAULT_MAX_WAIT_MINUTES)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("异步 Stable Audio 生成失败: prompt='{}', duration={}秒", prompt, duration, throwable);
                    } else {
                        log.info("异步 Stable Audio 生成成功: prompt='{}', duration={}秒, audioUrl={}",
                                prompt, duration, result.getAudioUrl());
                    }
                });
    }
}
